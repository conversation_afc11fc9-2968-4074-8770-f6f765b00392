/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import fs from 'fs';
import path from 'path';
import os from 'os';
import { AppConfig, ProviderCredentials, ChatSession } from '../types/index.js';
import { ToolRegistry } from '../tools/tool-registry.js';
import { FileDiscoveryService } from '../utils/fileDiscovery.js';

export enum ApprovalMode {
  DEFAULT = 'default',
  ALWAYS = 'always',
  NEVER = 'never',
}

export interface MCPServerConfig {
  command?: string;
  args?: string[];
  url?: string;
  httpUrl?: string;
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  trust?: boolean;
  headers?: Record<string, string>;
}

export class Config {
  private configPath: string;
  private config: AppConfig;
  private toolRegistry: ToolRegistry;
  private fileService: FileDiscoveryService;

  constructor(targetDir: string = process.cwd()) {
    this.configPath = path.join(os.homedir(), '.arien', 'config.json');
    this.config = this.loadConfig();
    this.toolRegistry = new ToolRegistry(this);
    this.fileService = new FileDiscoveryService(targetDir);
  }

  private loadConfig(): AppConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        return JSON.parse(configData);
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
    }

    return {
      providers: {},
      chatHistory: [],
      preferences: {
        autoSave: true,
        confirmToolCalls: true,
        streamingEnabled: true,
        maxHistoryLength: 100,
      },
    };
  }

  private saveConfig(): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  }

  // Provider management
  setProviderCredentials(provider: string, credentials: ProviderCredentials): void {
    this.config.providers[provider] = credentials;
    this.saveConfig();
  }

  getProviderCredentials(provider: string): ProviderCredentials | undefined {
    return this.config.providers[provider];
  }

  removeProviderCredentials(provider: string): void {
    delete this.config.providers[provider];
    this.saveConfig();
  }

  getConfiguredProviders(): string[] {
    return Object.keys(this.config.providers);
  }

  // Selection management
  setSelectedProvider(provider: string): void {
    this.config.selectedProvider = provider;
    this.saveConfig();
  }

  getSelectedProvider(): string | undefined {
    return this.config.selectedProvider;
  }

  setSelectedModel(model: string): void {
    this.config.selectedModel = model;
    this.saveConfig();
  }

  getSelectedModel(): string | undefined {
    return this.config.selectedModel;
  }

  setSelectedTheme(theme: string): void {
    this.config.selectedTheme = theme;
    this.saveConfig();
  }

  getSelectedTheme(): string | undefined {
    return this.config.selectedTheme;
  }

  // Chat history management
  addChatSession(session: ChatSession): void {
    this.config.chatHistory.push(session);
    
    // Limit history length
    if (this.config.chatHistory.length > this.config.preferences.maxHistoryLength) {
      this.config.chatHistory = this.config.chatHistory.slice(-this.config.preferences.maxHistoryLength);
    }
    
    this.saveConfig();
  }

  getChatHistory(): ChatSession[] {
    return this.config.chatHistory;
  }

  clearChatHistory(): void {
    this.config.chatHistory = [];
    this.saveConfig();
  }

  // Preferences
  getPreferences() {
    return this.config.preferences;
  }

  updatePreferences(preferences: Partial<typeof this.config.preferences>): void {
    this.config.preferences = { ...this.config.preferences, ...preferences };
    this.saveConfig();
  }

  // Tool system integration (maintaining compatibility with existing code)
  getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }

  getFileService(): FileDiscoveryService {
    return this.fileService;
  }

  // Legacy methods for backward compatibility
  getTargetDir(): string {
    return process.cwd();
  }

  getApprovalMode(): ApprovalMode {
    return this.config.preferences.confirmToolCalls ? ApprovalMode.DEFAULT : ApprovalMode.NEVER;
  }

  setApprovalMode(mode: ApprovalMode): void {
    this.config.preferences.confirmToolCalls = mode !== ApprovalMode.NEVER;
    this.saveConfig();
  }

  getSandbox(): boolean {
    return false; // Sandbox mode not implemented in this version
  }

  getDebugMode(): boolean {
    return process.env.DEBUG === '1' || process.env.NODE_ENV === 'development';
  }

  getQuestion(): string | undefined {
    return undefined; // Not used in interactive mode
  }

  getFullContext(): boolean {
    return true; // Always use full context
  }

  getToolDiscoveryCommand(): string | undefined {
    return process.env.ARIEN_TOOL_DISCOVERY_COMMAND;
  }

  getToolCallCommand(): string | undefined {
    return process.env.ARIEN_TOOL_CALL_COMMAND;
  }

  getMcpServerCommand(): string | undefined {
    return process.env.ARIEN_MCP_SERVER_COMMAND;
  }

  getMcpServers(): Record<string, MCPServerConfig> | undefined {
    const mcpConfig = process.env.ARIEN_MCP_SERVERS;
    if (mcpConfig) {
      try {
        return JSON.parse(mcpConfig);
      } catch (error) {
        console.warn('Failed to parse MCP servers config:', error);
      }
    }
    return undefined;
  }

  getUserAgent(): string {
    return 'Arien CLI/1.0.0';
  }

  getUserMemory(): string {
    return ''; // Memory management handled separately
  }

  setUserMemory(memory: string): void {
    // Memory management handled separately
  }

  getGeminiMdFileCount(): number {
    return 0; // Legacy Gemini-specific functionality
  }

  setGeminiMdFileCount(count: number): void {
    // Legacy Gemini-specific functionality
  }

  getFileFilteringRespectGitIgnore(): boolean {
    return true;
  }

  // Legacy Gemini client method for backward compatibility
  getGeminiClient(): any {
    return null; // Will be replaced by provider-specific clients
  }

  getApiKey(): string | undefined {
    const provider = this.getSelectedProvider();
    if (provider) {
      const credentials = this.getProviderCredentials(provider);
      return credentials?.apiKey;
    }
    return undefined;
  }

  getModel(): string | undefined {
    return this.getSelectedModel();
  }
}
