#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { render } from 'ink';
import { App } from './components/App.js';
import { Config } from '@arien/arien-cli-core';

async function main() {
  try {
    // Initialize configuration
    const config = new Config();
    
    // Render the main application
    const { waitUntilExit } = render(React.createElement(App, { config }));
    
    // Wait for the application to exit
    await waitUntilExit();
  } catch (error) {
    console.error('Failed to start Arien CLI:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

main().catch((error) => {
  console.error('Application error:', error);
  process.exit(1);
});
