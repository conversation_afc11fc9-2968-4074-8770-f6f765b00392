/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { BaseLLMClient, FunctionDeclaration } from './base.js';
import { 
  LLMMessage, 
  LLMResponse, 
  StreamingLLMResponse, 
  LLMClientConfig,
  LLMProvider,
  LLMModel,
  AuthMethod
} from '../types/index.js';

export const DEEPSEEK_PROVIDER: LLMProvider = {
  name: 'deepseek',
  displayName: 'DeepSeek',
  models: [
    {
      id: 'deepseek-chat',
      name: 'deepseek-chat',
      displayName: 'DeepSeek Chat',
      contextWindow: 32768,
      maxTokens: 4096,
      supportsFunctionCalling: true
    },
    {
      id: 'deepseek-reasoner',
      name: 'deepseek-reasoner',
      displayName: 'DeepSeek Reasoner',
      contextWindow: 32768,
      maxTokens: 4096,
      supportsFunctionCalling: true
    }
  ],
  authMethods: [
    {
      type: 'api_key',
      name: 'API Key',
      description: 'DeepSeek API key from platform.deepseek.com',
      required: true
    }
  ],
  supportsStreaming: true,
  supportsFunctionCalling: true
};

export class DeepSeekClient extends BaseLLMClient {
  private baseUrl: string;

  constructor(config: LLMClientConfig) {
    super(config, DEEPSEEK_PROVIDER);
    this.baseUrl = config.baseUrl || 'https://api.deepseek.com/v1';
  }

  async generateResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): Promise<LLMResponse> {
    try {
      const requestBody = {
        model: this.config.model,
        messages: this.formatMessages(messages),
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4096,
        ...(functions && functions.length > 0 && {
          tools: this.formatFunctions(functions),
          tool_choice: 'auto'
        })
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: abortSignal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DeepSeek API error (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];

      return {
        content: choice.message.content || '',
        toolCalls: this.parseToolCalls(choice.message),
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens
        } : undefined,
        finishReason: choice.finish_reason
      };
    } catch (error: any) {
      throw this.handleError(error, 'generateResponse');
    }
  }

  async *generateStreamingResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): AsyncGenerator<StreamingLLMResponse> {
    try {
      const requestBody = {
        model: this.config.model,
        messages: this.formatMessages(messages),
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4096,
        stream: true,
        ...(functions && functions.length > 0 && {
          tools: this.formatFunctions(functions),
          tool_choice: 'auto'
        })
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: abortSignal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DeepSeek API error (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('data: ')) {
              const data = trimmed.slice(6);
              if (data === '[DONE]') {
                yield { content: '', done: true };
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const choice = parsed.choices[0];
                
                if (choice.delta) {
                  yield {
                    content: choice.delta.content || '',
                    toolCalls: this.parseToolCalls(choice.delta),
                    done: false,
                    usage: parsed.usage ? {
                      promptTokens: parsed.usage.prompt_tokens,
                      completionTokens: parsed.usage.completion_tokens,
                      totalTokens: parsed.usage.total_tokens
                    } : undefined
                  };
                }
              } catch (parseError) {
                // Skip invalid JSON lines
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error: any) {
      throw this.handleError(error, 'generateStreamingResponse');
    }
  }

  async validateCredentials(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}
