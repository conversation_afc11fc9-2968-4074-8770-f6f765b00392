/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';
import { Config, ProviderRegistry, AuthManager, LLMMessage, BaseLLMClient } from '@arien/arien-cli-core';

interface ChatScreenProps {
  config: Config;
  provider: string;
  model: string;
  theme?: string;
  onBackToTheme: () => void;
  onError: (error: string) => void;
}

interface ChatMessage extends LLMMessage {
  id: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ 
  config,
  provider,
  model,
  theme = 'default',
  onBackToTheme,
  onError
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [client, setClient] = useState<BaseLLMClient | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const authManager = new AuthManager();

  useEffect(() => {
    initializeClient();
  }, [provider, model]);

  useEffect(() => {
    // Add welcome message
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        role: 'assistant',
        content: `Welcome to Arien CLI! I'm connected to ${ProviderRegistry.getProvider(provider)?.displayName} using the ${ProviderRegistry.getModelInfo(provider, model)?.displayName} model.\n\nI have access to various tools including file operations, web search, git integration, and more. How can I help you today?`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, []);

  const initializeClient = async () => {
    try {
      const credentials = await authManager.getCredentials(provider);
      if (!credentials) {
        onError('No credentials found for provider. Please re-authenticate.');
        return;
      }

      const newClient = ProviderRegistry.createClient(provider, {
        apiKey: credentials.apiKey,
        model,
        temperature: 0.7,
        maxTokens: 4096
      });

      setClient(newClient);
    } catch (error) {
      onError(`Failed to initialize client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  useInput((input, key) => {
    if (key.escape) {
      if (showHelp) {
        setShowHelp(false);
      } else {
        onBackToTheme();
      }
    } else if (key.ctrl && input === 'c') {
      process.exit(0);
    } else if (input === '?' && !currentInput) {
      setShowHelp(!showHelp);
    } else if (key.ctrl && input === 'l') {
      setMessages([]);
    }
  });

  const handleSendMessage = async () => {
    if (!currentInput.trim() || !client || isLoading) {
      return;
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: currentInput.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentInput('');
    setIsLoading(true);

    try {
      // Create assistant message for streaming
      const assistantMessageId = `assistant-${Date.now()}`;
      const assistantMessage: ChatMessage = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        isStreaming: true
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Prepare messages for API (exclude streaming message)
      const apiMessages = [...messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.toolCalls && { toolCalls: msg.toolCalls }),
        ...(msg.toolCallId && { toolCallId: msg.toolCallId })
      }));

      // Get available tools from config
      const toolRegistry = config.getToolRegistry();
      const availableTools = toolRegistry.getFunctionDeclarations();

      if (config.getPreferences().streamingEnabled) {
        // Streaming response
        const stream = client.generateStreamingResponse(apiMessages, availableTools);
        let fullContent = '';

        for await (const chunk of stream) {
          if (chunk.content) {
            fullContent += chunk.content;
            setMessages(prev => prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, content: fullContent }
                : msg
            ));
          }

          if (chunk.toolCalls) {
            // Handle tool calls
            await handleToolCalls(chunk.toolCalls, assistantMessageId);
          }

          if (chunk.done) {
            setMessages(prev => prev.map(msg => 
              msg.id === assistantMessageId 
                ? { ...msg, isStreaming: false }
                : msg
            ));
            break;
          }
        }
      } else {
        // Non-streaming response
        const response = await client.generateResponse(apiMessages, availableTools);
        
        setMessages(prev => prev.map(msg => 
          msg.id === assistantMessageId 
            ? { 
                ...msg, 
                content: response.content,
                toolCalls: response.toolCalls,
                isStreaming: false 
              }
            : msg
        ));

        if (response.toolCalls) {
          await handleToolCalls(response.toolCalls, assistantMessageId);
        }
      }
    } catch (error) {
      setMessages(prev => prev.filter(msg => msg.id !== `assistant-${Date.now()}`));
      onError(`Failed to get response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToolCalls = async (toolCalls: any[], messageId: string) => {
    const toolRegistry = config.getToolRegistry();
    
    for (const toolCall of toolCalls) {
      try {
        // Add tool call message
        const toolCallMessage: ChatMessage = {
          id: `tool-call-${toolCall.id}`,
          role: 'assistant',
          content: `🔧 Calling tool: ${toolCall.function.name}`,
          timestamp: new Date(),
          toolCalls: [toolCall]
        };
        
        setMessages(prev => [...prev, toolCallMessage]);

        // Execute tool
        const tool = toolRegistry.getTool(toolCall.function.name);
        if (tool) {
          const args = JSON.parse(toolCall.function.arguments);
          const result = await tool.execute(args);
          
          // Add tool result message
          const toolResultMessage: ChatMessage = {
            id: `tool-result-${toolCall.id}`,
            role: 'tool',
            content: typeof result === 'string' ? result : JSON.stringify(result),
            timestamp: new Date(),
            toolCallId: toolCall.id
          };
          
          setMessages(prev => [...prev, toolResultMessage]);
        } else {
          const errorMessage: ChatMessage = {
            id: `tool-error-${toolCall.id}`,
            role: 'tool',
            content: `Error: Tool '${toolCall.function.name}' not found`,
            timestamp: new Date(),
            toolCallId: toolCall.id
          };
          
          setMessages(prev => [...prev, errorMessage]);
        }
      } catch (error) {
        const errorMessage: ChatMessage = {
          id: `tool-error-${toolCall.id}`,
          role: 'tool',
          content: `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`,
          timestamp: new Date(),
          toolCallId: toolCall.id
        };
        
        setMessages(prev => [...prev, errorMessage]);
      }
    }
  };

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    const isTool = message.role === 'tool';
    
    let color = 'white';
    let prefix = '';
    
    if (isUser) {
      color = 'cyan';
      prefix = 'You: ';
    } else if (isSystem) {
      color = 'yellow';
      prefix = 'System: ';
    } else if (isTool) {
      color = 'green';
      prefix = '🔧 Tool: ';
    } else {
      color = 'white';
      prefix = 'Assistant: ';
    }

    return (
      <Box key={message.id} flexDirection="column" marginBottom={1}>
        <Box>
          <Text color={color} bold>{prefix}</Text>
          <Text color={color}>
            {message.content}
            {message.isStreaming && <Text color="gray">▋</Text>}
          </Text>
        </Box>
        <Box>
          <Text color="gray" dimColor>
            {message.timestamp.toLocaleTimeString()}
          </Text>
        </Box>
      </Box>
    );
  };

  const renderHelp = () => (
    <Box flexDirection="column" borderStyle="round" borderColor="yellow" padding={1} marginBottom={1}>
      <Box marginBottom={1}>
        <Text color="yellow" bold>Arien CLI Help</Text>
      </Box>
      
      <Box flexDirection="column">
        <Text color="white">Commands:</Text>
        <Text color="gray">  ? - Show/hide this help</Text>
        <Text color="gray">  Ctrl+L - Clear chat history</Text>
        <Text color="gray">  Ctrl+C - Exit application</Text>
        <Text color="gray">  ESC - Go back to theme selection</Text>
      </Box>
      
      <Box marginTop={1} flexDirection="column">
        <Text color="white">Current Configuration:</Text>
        <Text color="gray">  Provider: {ProviderRegistry.getProvider(provider)?.displayName}</Text>
        <Text color="gray">  Model: {ProviderRegistry.getModelInfo(provider, model)?.displayName}</Text>
        <Text color="gray">  Theme: {theme}</Text>
        <Text color="gray">  Streaming: {config.getPreferences().streamingEnabled ? 'Enabled' : 'Disabled'}</Text>
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box borderStyle="round" borderColor="cyan" padding={1} marginBottom={1}>
        <Box flexDirection="row" justifyContent="space-between" width="100%">
          <Box>
            <Text color="cyan" bold>
              Arien CLI - {ProviderRegistry.getProvider(provider)?.displayName}
            </Text>
          </Box>
          <Box>
            <Text color="gray">
              {ProviderRegistry.getModelInfo(provider, model)?.displayName} | Press ? for help
            </Text>
          </Box>
        </Box>
      </Box>

      {/* Help panel */}
      {showHelp && renderHelp()}

      {/* Messages */}
      <Box flexDirection="column" flexGrow={1} paddingX={1}>
        {messages.map(renderMessage)}
      </Box>

      {/* Input */}
      <Box borderStyle="round" borderColor={isLoading ? "yellow" : "green"} padding={1} marginTop={1}>
        <Box width="100%">
          <Text color="green">{'> '}</Text>
          <TextInput
            value={currentInput}
            onChange={setCurrentInput}
            onSubmit={handleSendMessage}
            placeholder={isLoading ? "Processing..." : "Type your message..."}
            showCursor={!isLoading}
          />
        </Box>
      </Box>
    </Box>
  );
};
