/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { useInput } from 'ink';

interface ErrorScreenProps {
  error: string;
  onRetry: () => void;
  onExit: () => void;
}

export const ErrorScreen: React.FC<ErrorScreenProps> = ({ 
  error, 
  onRetry, 
  onExit 
}) => {
  useInput((input, key) => {
    if (input === 'r' || input === 'R') {
      onRetry();
    } else if (key.escape || input === 'q' || input === 'Q') {
      onExit();
    }
  });

  return (
    <Box flexDirection="column" padding={2}>
      <Box marginBottom={2}>
        <Text color="red" bold>
          ❌ Error
        </Text>
      </Box>
      
      <Box marginBottom={2}>
        <Text color="red">{error}</Text>
      </Box>
      
      <Box flexDirection="column" marginTop={1}>
        <Text color="yellow">Press 'r' to retry</Text>
        <Text color="yellow">Press 'q' or ESC to exit</Text>
      </Box>
    </Box>
  );
};
