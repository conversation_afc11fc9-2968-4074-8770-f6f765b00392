/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import path from 'path';
import os from 'os';

export function makeRelative(absolutePath: string, basePath: string = process.cwd()): string {
  return path.relative(basePath, absolutePath);
}

export function shortenPath(filePath: string, maxLength: number = 50): string {
  if (filePath.length <= maxLength) {
    return filePath;
  }

  const parts = filePath.split(path.sep);
  if (parts.length <= 2) {
    return filePath;
  }

  // Try to keep the filename and some parent directories
  let result = parts[parts.length - 1];
  let index = parts.length - 2;

  while (index >= 0 && result.length + parts[index].length + 4 <= maxLength) {
    result = path.join(parts[index], result);
    index--;
  }

  if (index >= 0) {
    result = path.join('...', result);
  }

  return result;
}

export function getConfigDir(): string {
  return path.join(os.homedir(), '.arien');
}

export function getCacheDir(): string {
  const cacheDir = process.env.XDG_CACHE_HOME || path.join(os.homedir(), '.cache');
  return path.join(cacheDir, 'arien');
}

export function getDataDir(): string {
  const dataDir = process.env.XDG_DATA_HOME || path.join(os.homedir(), '.local', 'share');
  return path.join(dataDir, 'arien');
}
