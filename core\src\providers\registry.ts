/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { BaseLLMClient } from './base.js';
import { DeepSeekClient, DEEPSEEK_PROVIDER } from './deepseek.js';
import { OpenAIClient, OPENAI_PROVIDER } from './openai.js';
import { GeminiClient, GEMINI_PROVIDER } from './gemini.js';
import { AnthropicClient, ANTHROPIC_PROVIDER } from './anthropic.js';
import { LLMProvider, LLMClientConfig } from '../types/index.js';

export class ProviderRegistry {
  private static providers: Map<string, LLMProvider> = new Map([
    [DEEPSEEK_PROVIDER.name, DEEPSEEK_PROVIDER],
    [OPENAI_PROVIDER.name, OPENAI_PROVIDER],
    [GEMINI_PROVIDER.name, GEMINI_PROVIDER],
    [ANTHROPIC_PROVIDER.name, ANTHROPIC_PROVIDER]
  ]);

  static getProvider(name: string): LLMProvider | undefined {
    return this.providers.get(name);
  }

  static getAllProviders(): LLMProvider[] {
    return Array.from(this.providers.values());
  }

  static getProviderNames(): string[] {
    return Array.from(this.providers.keys());
  }

  static createClient(providerName: string, config: LLMClientConfig): BaseLLMClient {
    const provider = this.getProvider(providerName);
    if (!provider) {
      throw new Error(`Unknown provider: ${providerName}`);
    }

    switch (providerName) {
      case 'deepseek':
        return new DeepSeekClient(config);
      case 'openai':
        return new OpenAIClient(config);
      case 'gemini':
        return new GeminiClient(config);
      case 'anthropic':
        return new AnthropicClient(config);
      default:
        throw new Error(`No client implementation for provider: ${providerName}`);
    }
  }

  static getModelsForProvider(providerName: string): string[] {
    const provider = this.getProvider(providerName);
    return provider ? provider.models.map(m => m.id) : [];
  }

  static getModelInfo(providerName: string, modelId: string) {
    const provider = this.getProvider(providerName);
    return provider?.models.find(m => m.id === modelId);
  }

  static supportsStreaming(providerName: string): boolean {
    const provider = this.getProvider(providerName);
    return provider?.supportsStreaming || false;
  }

  static supportsFunctionCalling(providerName: string, modelId?: string): boolean {
    const provider = this.getProvider(providerName);
    if (!provider) return false;

    if (modelId) {
      const model = provider.models.find(m => m.id === modelId);
      return model?.supportsFunctionCalling || false;
    }

    return provider.supportsFunctionCalling;
  }

  static getAuthMethods(providerName: string) {
    const provider = this.getProvider(providerName);
    return provider?.authMethods || [];
  }
}
