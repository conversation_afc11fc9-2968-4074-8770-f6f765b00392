/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import Spinner from 'ink-spinner';
import BigText from 'ink-big-text';
import Gradient from 'ink-gradient';

interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Loading...' 
}) => {
  return (
    <Box flexDirection="column" alignItems="center" justifyContent="center" minHeight={20}>
      <Box marginBottom={2}>
        <Gradient name="rainbow">
          <BigText text="ARIEN" />
        </Gradient>
      </Box>
      
      <Box marginBottom={1}>
        <Text color="cyan">Multi-Provider LLM CLI</Text>
      </Box>
      
      <Box>
        <Spinner type="dots" />
        <Text> {message}</Text>
      </Box>
    </Box>
  );
};
