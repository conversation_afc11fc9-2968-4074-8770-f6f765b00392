/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import fs from 'fs';
import path from 'path';
import ignore from 'ignore';

export interface FileFilterOptions {
  respectGitIgnore?: boolean;
  additionalIgnorePatterns?: string[];
}

export class FileDiscoveryService {
  private rootDir: string;
  private gitIgnore: ReturnType<typeof ignore> | null = null;

  constructor(rootDir: string) {
    this.rootDir = path.resolve(rootDir);
    this.loadGitIgnore();
  }

  private loadGitIgnore(): void {
    const gitIgnorePath = path.join(this.rootDir, '.gitignore');
    if (fs.existsSync(gitIgnorePath)) {
      try {
        const gitIgnoreContent = fs.readFileSync(gitIgnorePath, 'utf8');
        this.gitIgnore = ignore().add(gitIgnoreContent);
      } catch (error) {
        console.warn('Failed to load .gitignore:', error);
      }
    }
  }

  filterFiles(files: string[], options: FileFilterOptions = {}): string[] {
    const { respectGitIgnore = true, additionalIgnorePatterns = [] } = options;
    
    let filteredFiles = files;

    // Apply git ignore patterns
    if (respectGitIgnore && this.gitIgnore) {
      filteredFiles = filteredFiles.filter(file => {
        const relativePath = path.relative(this.rootDir, path.resolve(this.rootDir, file));
        return !this.gitIgnore!.ignores(relativePath);
      });
    }

    // Apply additional ignore patterns
    if (additionalIgnorePatterns.length > 0) {
      const additionalIgnore = ignore().add(additionalIgnorePatterns);
      filteredFiles = filteredFiles.filter(file => {
        const relativePath = path.relative(this.rootDir, path.resolve(this.rootDir, file));
        return !additionalIgnore.ignores(relativePath);
      });
    }

    return filteredFiles;
  }

  isFileIgnored(filePath: string, options: FileFilterOptions = {}): boolean {
    const { respectGitIgnore = true, additionalIgnorePatterns = [] } = options;
    const relativePath = path.relative(this.rootDir, path.resolve(this.rootDir, filePath));

    if (respectGitIgnore && this.gitIgnore && this.gitIgnore.ignores(relativePath)) {
      return true;
    }

    if (additionalIgnorePatterns.length > 0) {
      const additionalIgnore = ignore().add(additionalIgnorePatterns);
      if (additionalIgnore.ignores(relativePath)) {
        return true;
      }
    }

    return false;
  }

  getRootDir(): string {
    return this.rootDir;
  }
}
