/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import SelectInput from 'ink-select-input';
import { Config, Theme } from '@arien/arien-cli-core';

interface ThemeScreenProps {
  config: Config;
  selectedProvider: string;
  selectedModel: string;
  onThemeSelected: (theme: string) => void;
  onBack: () => void;
  onError: (error: string) => void;
}

const AVAILABLE_THEMES: Theme[] = [
  {
    name: 'default',
    displayName: 'Default',
    colors: {
      primary: '#00D9FF',
      secondary: '#FF6B6B',
      accent: '#4ECDC4',
      background: '#1A1A1A',
      text: '#FFFFFF',
      error: '#FF5555',
      warning: '#FFB86C',
      success: '#50FA7B'
    }
  },
  {
    name: 'ocean',
    displayName: 'Ocean Blue',
    colors: {
      primary: '#0077BE',
      secondary: '#00A8CC',
      accent: '#7FDBFF',
      background: '#001F3F',
      text: '#FFFFFF',
      error: '#FF4136',
      warning: '#FF851B',
      success: '#2ECC40'
    }
  },
  {
    name: 'forest',
    displayName: 'Forest Green',
    colors: {
      primary: '#2ECC40',
      secondary: '#3D9970',
      accent: '#01FF70',
      background: '#111111',
      text: '#FFFFFF',
      error: '#FF4136',
      warning: '#FF851B',
      success: '#2ECC40'
    }
  },
  {
    name: 'sunset',
    displayName: 'Sunset Orange',
    colors: {
      primary: '#FF851B',
      secondary: '#FF6B35',
      accent: '#FFD23F',
      background: '#2C1810',
      text: '#FFFFFF',
      error: '#FF4136',
      warning: '#FF851B',
      success: '#2ECC40'
    }
  },
  {
    name: 'purple',
    displayName: 'Royal Purple',
    colors: {
      primary: '#B10DC9',
      secondary: '#9B59B6',
      accent: '#E91E63',
      background: '#1A0D1A',
      text: '#FFFFFF',
      error: '#FF4136',
      warning: '#FF851B',
      success: '#2ECC40'
    }
  },
  {
    name: 'minimal',
    displayName: 'Minimal Gray',
    colors: {
      primary: '#AAAAAA',
      secondary: '#888888',
      accent: '#DDDDDD',
      background: '#111111',
      text: '#FFFFFF',
      error: '#FF5555',
      warning: '#FFAA55',
      success: '#55FF55'
    }
  }
];

export const ThemeScreen: React.FC<ThemeScreenProps> = ({ 
  config,
  selectedProvider,
  selectedModel,
  onThemeSelected,
  onBack,
  onError
}) => {
  const [selectedTheme, setSelectedTheme] = useState<string>('default');
  const [previewMode, setPreviewMode] = useState(false);

  useInput((input, key) => {
    if (key.escape) {
      if (previewMode) {
        setPreviewMode(false);
      } else {
        onBack();
      }
    } else if (input === 'p' || input === 'P') {
      setPreviewMode(!previewMode);
    }
  });

  const handleThemeSelect = (item: { value: string }) => {
    setSelectedTheme(item.value);
    if (!previewMode) {
      onThemeSelected(item.value);
    }
  };

  const handleConfirmTheme = () => {
    onThemeSelected(selectedTheme);
  };

  const getCurrentTheme = (): Theme => {
    return AVAILABLE_THEMES.find(t => t.name === selectedTheme) || AVAILABLE_THEMES[0];
  };

  const renderThemePreview = (theme: Theme) => {
    return (
      <Box flexDirection="column" borderStyle="round" borderColor={theme.colors.primary} padding={1} marginBottom={1}>
        <Box marginBottom={1}>
          <Text color={theme.colors.primary} bold>{theme.displayName}</Text>
        </Box>
        
        <Box flexDirection="column">
          <Box>
            <Text color={theme.colors.primary}>Primary: </Text>
            <Text color={theme.colors.primary}>Sample text</Text>
          </Box>
          <Box>
            <Text color={theme.colors.secondary}>Secondary: </Text>
            <Text color={theme.colors.secondary}>Sample text</Text>
          </Box>
          <Box>
            <Text color={theme.colors.accent}>Accent: </Text>
            <Text color={theme.colors.accent}>Sample text</Text>
          </Box>
          <Box>
            <Text color={theme.colors.success}>Success: </Text>
            <Text color={theme.colors.success}>Operation completed</Text>
          </Box>
          <Box>
            <Text color={theme.colors.warning}>Warning: </Text>
            <Text color={theme.colors.warning}>Please check this</Text>
          </Box>
          <Box>
            <Text color={theme.colors.error}>Error: </Text>
            <Text color={theme.colors.error}>Something went wrong</Text>
          </Box>
        </Box>
      </Box>
    );
  };

  const renderThemeSelection = () => {
    const themeItems = AVAILABLE_THEMES.map(theme => ({
      label: theme.displayName,
      value: theme.name
    }));

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>Select Theme</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Provider: {selectedProvider}</Text>
          <Text color="gray">Model: {selectedModel}</Text>
        </Box>
        
        <SelectInput items={themeItems} onSelect={handleThemeSelect} />
        
        <Box marginTop={2} flexDirection="column">
          <Text color="gray">Use ↑↓ to navigate, Enter to select</Text>
          <Text color="gray">Press 'p' to preview themes, ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderThemePreviewMode = () => {
    const currentTheme = getCurrentTheme();
    
    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>Theme Preview</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Use ↑↓ to browse themes, Enter to confirm, ESC to go back</Text>
        </Box>
        
        <Box flexDirection="row">
          <Box flexDirection="column" width="50%">
            <Box marginBottom={2}>
              <Text color="yellow" bold>Available Themes:</Text>
            </Box>
            
            {AVAILABLE_THEMES.map(theme => (
              <Box key={theme.name} marginBottom={1}>
                <Text color={theme.name === selectedTheme ? theme.colors.primary : 'gray'}>
                  {theme.name === selectedTheme ? '► ' : '  '}
                  {theme.displayName}
                </Text>
              </Box>
            ))}
            
            <Box marginTop={2}>
              <Text color="yellow">Press Enter to confirm this theme</Text>
            </Box>
          </Box>
          
          <Box flexDirection="column" width="50%" paddingLeft={2}>
            <Box marginBottom={2}>
              <Text color="yellow" bold>Preview:</Text>
            </Box>
            
            {renderThemePreview(currentTheme)}
            
            <Box marginTop={1} borderStyle="round" borderColor={currentTheme.colors.accent} padding={1}>
              <Box flexDirection="column">
                <Text color={currentTheme.colors.primary} bold>Chat Interface Preview</Text>
                <Box marginTop={1}>
                  <Text color={currentTheme.colors.text}>User: Hello, how are you?</Text>
                </Box>
                <Box>
                  <Text color={currentTheme.colors.secondary}>Assistant: I'm doing well, thank you!</Text>
                </Box>
                <Box marginTop={1}>
                  <Text color={currentTheme.colors.accent}>🔧 Tool call: search_web</Text>
                </Box>
                <Box>
                  <Text color={currentTheme.colors.success}>✓ Tool completed successfully</Text>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    );
  };

  // Handle theme navigation in preview mode
  useInput((input, key) => {
    if (previewMode) {
      const currentIndex = AVAILABLE_THEMES.findIndex(t => t.name === selectedTheme);
      
      if (key.upArrow && currentIndex > 0) {
        setSelectedTheme(AVAILABLE_THEMES[currentIndex - 1].name);
      } else if (key.downArrow && currentIndex < AVAILABLE_THEMES.length - 1) {
        setSelectedTheme(AVAILABLE_THEMES[currentIndex + 1].name);
      } else if (key.return) {
        handleConfirmTheme();
      }
    }
  });

  return previewMode ? renderThemePreviewMode() : renderThemeSelection();
};
