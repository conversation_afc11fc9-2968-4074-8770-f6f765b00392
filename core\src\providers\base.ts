/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { 
  LLMMessage, 
  LLMResponse, 
  StreamingLLMResponse, 
  LLMClientConfig, 
  ToolCall,
  LLMProvider,
  LLMModel 
} from '../types/index.js';

export interface FunctionDeclaration {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

export abstract class BaseLLMClient {
  protected config: LLMClientConfig;
  protected provider: LLMProvider;

  constructor(config: LLMClientConfig, provider: LLMProvider) {
    this.config = config;
    this.provider = provider;
  }

  abstract generateResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): Promise<LLMResponse>;

  abstract generateStreamingResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): AsyncGenerator<StreamingLLMResponse>;

  abstract validateCredentials(): Promise<boolean>;

  getProvider(): LLMProvider {
    return this.provider;
  }

  getModel(): string {
    return this.config.model;
  }

  protected formatMessages(messages: LLMMessage[]): any[] {
    // Default implementation - override in subclasses for provider-specific formatting
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.toolCalls && { tool_calls: msg.toolCalls }),
      ...(msg.toolCallId && { tool_call_id: msg.toolCallId })
    }));
  }

  protected formatFunctions(functions: FunctionDeclaration[]): any[] {
    // Default implementation - override in subclasses for provider-specific formatting
    return functions.map(func => ({
      type: 'function',
      function: {
        name: func.name,
        description: func.description,
        parameters: func.parameters
      }
    }));
  }

  protected parseToolCalls(response: any): ToolCall[] | undefined {
    // Default implementation - override in subclasses for provider-specific parsing
    if (response.tool_calls) {
      return response.tool_calls.map((call: any) => ({
        id: call.id || `call_${Date.now()}`,
        type: 'function',
        function: {
          name: call.function.name,
          arguments: typeof call.function.arguments === 'string' 
            ? call.function.arguments 
            : JSON.stringify(call.function.arguments)
        }
      }));
    }
    return undefined;
  }

  protected handleError(error: any, context: string): Error {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;
      return new Error(`${this.provider.name} API error (${status}): ${message}`);
    }
    return new Error(`${this.provider.name} ${context}: ${error.message}`);
  }
}
