/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import SelectInput from 'ink-select-input';
import TextInput from 'ink-text-input';
import BigText from 'ink-big-text';
import Gradient from 'ink-gradient';
import { Config, ProviderRegistry, AuthManager } from '@arien/arien-cli-core';

interface AuthScreenProps {
  config: Config;
  onAuthComplete: (provider: string, model: string) => void;
  onError: (error: string) => void;
}

type AuthStep = 'welcome' | 'provider' | 'model' | 'auth_method' | 'api_key' | 'oauth' | 'authenticating';

export const AuthScreen: React.FC<AuthScreenProps> = ({ 
  config, 
  onAuthComplete, 
  onError 
}) => {
  const [step, setStep] = useState<AuthStep>('welcome');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedAuthMethod, setSelectedAuthMethod] = useState<'api_key' | 'oauth2'>('api_key');
  const [apiKey, setApiKey] = useState('');
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const authManager = new AuthManager();

  useInput((input, key) => {
    if (key.escape && step !== 'welcome' && step !== 'authenticating') {
      // Go back to previous step
      switch (step) {
        case 'provider':
          setStep('welcome');
          break;
        case 'model':
          setStep('provider');
          break;
        case 'auth_method':
          setStep('model');
          break;
        case 'api_key':
        case 'oauth':
          setStep('auth_method');
          break;
      }
    }
  });

  const handleProviderSelect = (item: { value: string }) => {
    setSelectedProvider(item.value);
    setStep('model');
  };

  const handleModelSelect = (item: { value: string }) => {
    setSelectedModel(item.value);
    setStep('auth_method');
  };

  const handleAuthMethodSelect = (item: { value: 'api_key' | 'oauth2' }) => {
    setSelectedAuthMethod(item.value);
    if (item.value === 'api_key') {
      setStep('api_key');
    } else {
      setStep('oauth');
    }
  };

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      onError('API key cannot be empty');
      return;
    }

    await performAuthentication();
  };

  const handleOAuthStart = async () => {
    await performAuthentication();
  };

  const performAuthentication = async () => {
    setIsAuthenticating(true);
    setStep('authenticating');

    try {
      await authManager.authenticateProvider(
        selectedProvider, 
        selectedAuthMethod, 
        selectedAuthMethod === 'api_key' ? apiKey : undefined
      );

      // Store selections in config
      config.setSelectedProvider(selectedProvider);
      config.setSelectedModel(selectedModel);

      onAuthComplete(selectedProvider, selectedModel);
    } catch (error) {
      setIsAuthenticating(false);
      setStep(selectedAuthMethod === 'api_key' ? 'api_key' : 'oauth');
      onError(error instanceof Error ? error.message : 'Authentication failed');
    }
  };

  const renderWelcome = () => (
    <Box flexDirection="column" alignItems="center" justifyContent="center" minHeight={20}>
      <Box marginBottom={2}>
        <Gradient name="rainbow">
          <BigText text="ARIEN" />
        </Gradient>
      </Box>
      
      <Box marginBottom={1}>
        <Text color="cyan">Multi-Provider LLM CLI</Text>
      </Box>
      
      <Box marginBottom={2}>
        <Text color="gray">Welcome! Let's set up your first LLM provider.</Text>
      </Box>
      
      <Box>
        <Text color="yellow">Press any key to continue...</Text>
      </Box>
    </Box>
  );

  const renderProviderSelection = () => {
    const providers = ProviderRegistry.getAllProviders().map(provider => ({
      label: `${provider.displayName} (${provider.models.length} models)`,
      value: provider.name
    }));

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={2}>
          <Text color="cyan" bold>Select LLM Provider</Text>
        </Box>
        
        <SelectInput items={providers} onSelect={handleProviderSelect} />
        
        <Box marginTop={2}>
          <Text color="gray">Use ↑↓ to navigate, Enter to select, ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderModelSelection = () => {
    const provider = ProviderRegistry.getProvider(selectedProvider);
    if (!provider) return null;

    const models = provider.models.map(model => ({
      label: `${model.displayName} (${model.contextWindow.toLocaleString()} tokens)`,
      value: model.id
    }));

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>Select Model</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Provider: {provider.displayName}</Text>
        </Box>
        
        <SelectInput items={models} onSelect={handleModelSelect} />
        
        <Box marginTop={2}>
          <Text color="gray">Use ↑↓ to navigate, Enter to select, ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderAuthMethodSelection = () => {
    const provider = ProviderRegistry.getProvider(selectedProvider);
    if (!provider) return null;

    const authMethods = provider.authMethods.map(method => ({
      label: `${method.name} - ${method.description}`,
      value: method.type
    }));

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>Select Authentication Method</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Provider: {provider.displayName}</Text>
          <Text color="gray">Model: {provider.models.find(m => m.id === selectedModel)?.displayName}</Text>
        </Box>
        
        <SelectInput items={authMethods} onSelect={handleAuthMethodSelect} />
        
        <Box marginTop={2}>
          <Text color="gray">Use ↑↓ to navigate, Enter to select, ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderApiKeyInput = () => {
    const provider = ProviderRegistry.getProvider(selectedProvider);
    if (!provider) return null;

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>Enter API Key</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Provider: {provider.displayName}</Text>
          <Text color="gray">Model: {provider.models.find(m => m.id === selectedModel)?.displayName}</Text>
        </Box>
        
        <Box marginBottom={1}>
          <Text>API Key: </Text>
          <TextInput 
            value={apiKey} 
            onChange={setApiKey} 
            onSubmit={handleApiKeySubmit}
            mask="*"
            placeholder="Enter your API key..."
          />
        </Box>
        
        <Box marginTop={2}>
          <Text color="gray">Enter your API key and press Enter, or ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderOAuthFlow = () => {
    const provider = ProviderRegistry.getProvider(selectedProvider);
    if (!provider) return null;

    return (
      <Box flexDirection="column" padding={2}>
        <Box marginBottom={1}>
          <Text color="cyan" bold>OAuth2 Authentication</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text color="gray">Provider: {provider.displayName}</Text>
          <Text color="gray">Model: {provider.models.find(m => m.id === selectedModel)?.displayName}</Text>
        </Box>
        
        <Box marginBottom={2}>
          <Text>This will open your browser for authentication.</Text>
        </Box>
        
        <Box>
          <Text color="yellow">Press Enter to start OAuth flow, or ESC to go back</Text>
        </Box>
      </Box>
    );
  };

  const renderAuthenticating = () => (
    <Box flexDirection="column" alignItems="center" justifyContent="center" minHeight={10}>
      <Box marginBottom={2}>
        <Text color="cyan" bold>Authenticating...</Text>
      </Box>
      
      <Box>
        <Text color="gray">
          {selectedAuthMethod === 'oauth2' 
            ? 'Please complete authentication in your browser'
            : 'Validating API key...'
          }
        </Text>
      </Box>
    </Box>
  );

  // Handle welcome screen input
  useInput((input, key) => {
    if (step === 'welcome' && (input || key.return)) {
      setStep('provider');
    } else if (step === 'oauth' && key.return) {
      handleOAuthStart();
    }
  });

  switch (step) {
    case 'welcome':
      return renderWelcome();
    case 'provider':
      return renderProviderSelection();
    case 'model':
      return renderModelSelection();
    case 'auth_method':
      return renderAuthMethodSelection();
    case 'api_key':
      return renderApiKeyInput();
    case 'oauth':
      return renderOAuthFlow();
    case 'authenticating':
      return renderAuthenticating();
    default:
      return <Text color="red">Unknown step: {step}</Text>;
  }
};
