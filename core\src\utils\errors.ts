/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

export class ArienError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'ArienError';
  }
}

export class AuthenticationError extends ArienError {
  constructor(message: string, provider?: string) {
    super(message, 'AUTH_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class ProviderError extends ArienError {
  constructor(message: string, provider: string, public statusCode?: number) {
    super(`${provider}: ${message}`, 'PROVIDER_ERROR');
    this.name = 'ProviderError';
  }
}

export class ConfigurationError extends ArienError {
  constructor(message: string) {
    super(message, 'CONFIG_ERROR');
    this.name = 'ConfigurationError';
  }
}
