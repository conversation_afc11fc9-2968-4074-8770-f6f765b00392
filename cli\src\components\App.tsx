/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { Config } from '@arien/arien-cli-core';
import { AuthScreen } from './AuthScreen.js';
import { ThemeScreen } from './ThemeScreen.js';
import { ChatScreen } from './ChatScreen.js';
import { LoadingScreen } from './LoadingScreen.js';
import { ErrorScreen } from './ErrorScreen.js';
import { UIState } from '@arien/arien-cli-core';

interface AppProps {
  config: Config;
}

export const App: React.FC<AppProps> = ({ config }) => {
  const [uiState, setUIState] = useState<UIState>({
    currentScreen: 'auth',
    isAuthenticated: false,
    isLoading: true,
    error: undefined
  });

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setUIState(prev => ({ ...prev, isLoading: true, error: undefined }));

      // Check if user has any configured providers
      const configuredProviders = config.getConfiguredProviders();
      const selectedProvider = config.getSelectedProvider();
      const selectedModel = config.getSelectedModel();
      const selectedTheme = config.getSelectedTheme();

      if (configuredProviders.length === 0) {
        // No providers configured, show auth screen
        setUIState(prev => ({
          ...prev,
          currentScreen: 'auth',
          isAuthenticated: false,
          isLoading: false
        }));
      } else if (!selectedProvider || !selectedModel) {
        // Providers configured but no selection made, show theme screen
        setUIState(prev => ({
          ...prev,
          currentScreen: 'theme',
          isAuthenticated: true,
          isLoading: false
        }));
      } else {
        // Everything configured, go to chat
        setUIState(prev => ({
          ...prev,
          currentScreen: 'chat',
          selectedProvider,
          selectedModel,
          selectedTheme,
          isAuthenticated: true,
          isLoading: false
        }));
      }
    } catch (error) {
      setUIState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        isLoading: false
      }));
    }
  };

  const handleAuthComplete = (provider: string, model: string) => {
    setUIState(prev => ({
      ...prev,
      selectedProvider: provider,
      selectedModel: model,
      isAuthenticated: true,
      currentScreen: 'theme'
    }));
  };

  const handleThemeSelected = (theme: string) => {
    config.setSelectedTheme(theme);
    setUIState(prev => ({
      ...prev,
      selectedTheme: theme,
      currentScreen: 'chat'
    }));
  };

  const handleBackToAuth = () => {
    setUIState(prev => ({
      ...prev,
      currentScreen: 'auth',
      isAuthenticated: false,
      selectedProvider: undefined,
      selectedModel: undefined
    }));
  };

  const handleBackToTheme = () => {
    setUIState(prev => ({
      ...prev,
      currentScreen: 'theme'
    }));
  };

  const handleError = (error: string) => {
    setUIState(prev => ({
      ...prev,
      error
    }));
  };

  const handleClearError = () => {
    setUIState(prev => ({
      ...prev,
      error: undefined
    }));
  };

  if (uiState.error) {
    return (
      <ErrorScreen 
        error={uiState.error} 
        onRetry={handleClearError}
        onExit={() => process.exit(0)}
      />
    );
  }

  if (uiState.isLoading) {
    return <LoadingScreen message="Initializing Arien CLI..." />;
  }

  switch (uiState.currentScreen) {
    case 'auth':
      return (
        <AuthScreen 
          config={config}
          onAuthComplete={handleAuthComplete}
          onError={handleError}
        />
      );

    case 'theme':
      return (
        <ThemeScreen 
          config={config}
          selectedProvider={uiState.selectedProvider!}
          selectedModel={uiState.selectedModel!}
          onThemeSelected={handleThemeSelected}
          onBack={handleBackToAuth}
          onError={handleError}
        />
      );

    case 'chat':
      return (
        <ChatScreen 
          config={config}
          provider={uiState.selectedProvider!}
          model={uiState.selectedModel!}
          theme={uiState.selectedTheme}
          onBackToTheme={handleBackToTheme}
          onError={handleError}
        />
      );

    default:
      return (
        <Box>
          <Text color="red">Unknown screen: {uiState.currentScreen}</Text>
        </Box>
      );
  }
};
