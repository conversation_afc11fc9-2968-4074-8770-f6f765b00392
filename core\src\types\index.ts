/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export interface LLMProvider {
  name: string;
  displayName: string;
  models: LLMModel[];
  authMethods: AuthMethod[];
  supportsStreaming: boolean;
  supportsFunctionCalling: boolean;
}

export interface LLMModel {
  id: string;
  name: string;
  displayName: string;
  contextWindow: number;
  maxTokens: number;
  supportsFunctionCalling: boolean;
  supportsVision?: boolean;
}

export interface AuthMethod {
  type: 'api_key' | 'oauth2' | 'bearer_token';
  name: string;
  description: string;
  required: boolean;
}

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface LLMResponse {
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: 'stop' | 'length' | 'tool_calls' | 'content_filter';
}

export interface StreamingLLMResponse {
  content?: string;
  toolCalls?: ToolCall[];
  done: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface LLMClientConfig {
  apiKey?: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface ProviderCredentials {
  provider: string;
  apiKey?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
  additionalData?: Record<string, unknown>;
}

export interface ChatSession {
  id: string;
  provider: string;
  model: string;
  messages: LLMMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Theme {
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
    error: string;
    warning: string;
    success: string;
  };
}

export interface UIState {
  currentScreen: 'auth' | 'theme' | 'chat';
  selectedProvider?: string;
  selectedModel?: string;
  selectedTheme?: string;
  isAuthenticated: boolean;
  isLoading: boolean;
  error?: string;
}

export interface AppConfig {
  providers: Record<string, ProviderCredentials>;
  selectedProvider?: string;
  selectedModel?: string;
  selectedTheme?: string;
  chatHistory: ChatSession[];
  preferences: {
    autoSave: boolean;
    confirmToolCalls: boolean;
    streamingEnabled: boolean;
    maxHistoryLength: number;
  };
}
