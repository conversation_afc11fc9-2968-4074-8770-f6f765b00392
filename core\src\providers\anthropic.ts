/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { BaseLLMClient, FunctionDeclaration } from './base.js';
import { 
  LLMMessage, 
  LLMResponse, 
  StreamingLLMResponse, 
  LLMClientConfig,
  LLMProvider,
  LLMModel,
  AuthMethod
} from '../types/index.js';

export const ANTHROPIC_PROVIDER: LLMProvider = {
  name: 'anthropic',
  displayName: 'Anthropic',
  models: [
    {
      id: 'claude-3-haiku-20240307',
      name: 'claude-3-haiku-20240307',
      displayName: 'Claude 3 Haiku',
      contextWindow: 200000,
      maxTokens: 4096,
      supportsFunctionCalling: true
    },
    {
      id: 'claude-3-sonnet-20240229',
      name: 'claude-3-sonnet-20240229',
      displayName: 'Claude 3 Sonnet',
      contextWindow: 200000,
      maxTokens: 4096,
      supportsFunctionCalling: true
    },
    {
      id: 'claude-3-opus-20240229',
      name: 'claude-3-opus-20240229',
      displayName: 'Claude 3 Opus',
      contextWindow: 200000,
      maxTokens: 4096,
      supportsFunctionCalling: true
    },
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'claude-3-5-sonnet-20241022',
      displayName: 'Claude 3.5 Sonnet',
      contextWindow: 200000,
      maxTokens: 8192,
      supportsFunctionCalling: true
    }
  ],
  authMethods: [
    {
      type: 'api_key',
      name: 'API Key',
      description: 'Anthropic API key from console.anthropic.com',
      required: true
    }
  ],
  supportsStreaming: true,
  supportsFunctionCalling: true
};

export class AnthropicClient extends BaseLLMClient {
  private baseUrl: string;
  private apiVersion: string;

  constructor(config: LLMClientConfig) {
    super(config, ANTHROPIC_PROVIDER);
    this.baseUrl = config.baseUrl || 'https://api.anthropic.com';
    this.apiVersion = '2023-06-01';
  }

  async generateResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): Promise<LLMResponse> {
    try {
      const { system, messages: formattedMessages } = this.formatMessagesForAnthropic(messages);
      
      const requestBody = {
        model: this.config.model,
        max_tokens: this.config.maxTokens || 4096,
        temperature: this.config.temperature || 0.7,
        messages: formattedMessages,
        ...(system && { system }),
        ...(functions && functions.length > 0 && {
          tools: this.formatFunctionsForAnthropic(functions)
        })
      };

      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.apiKey!,
          'anthropic-version': this.apiVersion,
          'User-Agent': 'Arien CLI/1.0.0'
        },
        body: JSON.stringify(requestBody),
        signal: abortSignal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Anthropic API error (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      let content = '';
      let toolCalls: any[] | undefined;

      for (const contentBlock of data.content) {
        if (contentBlock.type === 'text') {
          content += contentBlock.text;
        } else if (contentBlock.type === 'tool_use') {
          if (!toolCalls) toolCalls = [];
          toolCalls.push({
            id: contentBlock.id,
            type: 'function',
            function: {
              name: contentBlock.name,
              arguments: JSON.stringify(contentBlock.input)
            }
          });
        }
      }

      return {
        content,
        toolCalls,
        usage: data.usage ? {
          promptTokens: data.usage.input_tokens,
          completionTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens
        } : undefined,
        finishReason: this.mapFinishReason(data.stop_reason)
      };
    } catch (error: any) {
      throw this.handleError(error, 'generateResponse');
    }
  }

  async *generateStreamingResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): AsyncGenerator<StreamingLLMResponse> {
    try {
      const { system, messages: formattedMessages } = this.formatMessagesForAnthropic(messages);
      
      const requestBody = {
        model: this.config.model,
        max_tokens: this.config.maxTokens || 4096,
        temperature: this.config.temperature || 0.7,
        messages: formattedMessages,
        stream: true,
        ...(system && { system }),
        ...(functions && functions.length > 0 && {
          tools: this.formatFunctionsForAnthropic(functions)
        })
      };

      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.apiKey!,
          'anthropic-version': this.apiVersion,
          'User-Agent': 'Arien CLI/1.0.0'
        },
        body: JSON.stringify(requestBody),
        signal: abortSignal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Anthropic API error (${response.status}): ${errorData.error?.message || response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('data: ')) {
              const data = trimmed.slice(6);
              
              try {
                const parsed = JSON.parse(data);
                
                if (parsed.type === 'content_block_delta') {
                  if (parsed.delta.type === 'text_delta') {
                    yield {
                      content: parsed.delta.text,
                      done: false
                    };
                  }
                } else if (parsed.type === 'message_delta') {
                  if (parsed.delta.stop_reason) {
                    yield {
                      content: '',
                      done: true,
                      usage: parsed.usage ? {
                        promptTokens: parsed.usage.input_tokens,
                        completionTokens: parsed.usage.output_tokens,
                        totalTokens: parsed.usage.input_tokens + parsed.usage.output_tokens
                      } : undefined
                    };
                    return;
                  }
                }
              } catch (parseError) {
                // Skip invalid JSON lines
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error: any) {
      throw this.handleError(error, 'generateStreamingResponse');
    }
  }

  async validateCredentials(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.apiKey!,
          'anthropic-version': this.apiVersion
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'Hi' }]
        })
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  private formatMessagesForAnthropic(messages: LLMMessage[]): { system?: string; messages: any[] } {
    let system: string | undefined;
    const formattedMessages: any[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        system = msg.content;
      } else if (msg.role === 'tool') {
        // Tool response message
        formattedMessages.push({
          role: 'user',
          content: [
            {
              type: 'tool_result',
              tool_use_id: msg.toolCallId,
              content: msg.content
            }
          ]
        });
      } else {
        formattedMessages.push({
          role: msg.role,
          content: msg.content
        });
      }
    }

    return { system, messages: formattedMessages };
  }

  private formatFunctionsForAnthropic(functions: FunctionDeclaration[]): any[] {
    return functions.map(func => ({
      name: func.name,
      description: func.description,
      input_schema: func.parameters
    }));
  }

  private mapFinishReason(reason?: string): LLMResponse['finishReason'] {
    switch (reason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'tool_use':
        return 'tool_calls';
      case 'stop_sequence':
        return 'stop';
      default:
        return 'stop';
    }
  }
}
