/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import fs from 'fs';
import path from 'path';

export function isGitRepository(dir: string = process.cwd()): boolean {
  try {
    let currentDir = path.resolve(dir);
    
    while (currentDir !== path.dirname(currentDir)) {
      const gitDir = path.join(currentDir, '.git');
      if (fs.existsSync(gitDir)) {
        return true;
      }
      currentDir = path.dirname(currentDir);
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

export function findGitRoot(dir: string = process.cwd()): string | null {
  try {
    let currentDir = path.resolve(dir);
    
    while (currentDir !== path.dirname(currentDir)) {
      const gitDir = path.join(currentDir, '.git');
      if (fs.existsSync(gitDir)) {
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }
    
    return null;
  } catch (error) {
    return null;
  }
}
