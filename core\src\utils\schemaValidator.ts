/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export class SchemaValidator {
  static validateObject(obj: unknown, schema: Record<string, any>): string | null {
    if (typeof obj !== 'object' || obj === null) {
      return 'Expected an object';
    }

    const typedObj = obj as Record<string, unknown>;

    for (const [key, schemaValue] of Object.entries(schema.properties || {})) {
      const value = typedObj[key];
      const isRequired = schema.required?.includes(key);

      if (value === undefined) {
        if (isRequired) {
          return `Missing required property: ${key}`;
        }
        continue;
      }

      const error = this.validateValue(value, schemaValue, key);
      if (error) {
        return error;
      }
    }

    return null;
  }

  private static validateValue(value: unknown, schema: any, path: string): string | null {
    if (schema.type) {
      const expectedType = schema.type;
      const actualType = Array.isArray(value) ? 'array' : typeof value;

      if (expectedType === 'integer' && typeof value === 'number' && !Number.isInteger(value)) {
        return `${path}: Expected integer, got ${actualType}`;
      }

      if (expectedType === 'number' && typeof value !== 'number') {
        return `${path}: Expected number, got ${actualType}`;
      }

      if (expectedType === 'string' && typeof value !== 'string') {
        return `${path}: Expected string, got ${actualType}`;
      }

      if (expectedType === 'boolean' && typeof value !== 'boolean') {
        return `${path}: Expected boolean, got ${actualType}`;
      }

      if (expectedType === 'array' && !Array.isArray(value)) {
        return `${path}: Expected array, got ${actualType}`;
      }

      if (expectedType === 'object' && (typeof value !== 'object' || value === null || Array.isArray(value))) {
        return `${path}: Expected object, got ${actualType}`;
      }
    }

    if (schema.enum && !schema.enum.includes(value)) {
      return `${path}: Value must be one of: ${schema.enum.join(', ')}`;
    }

    if (schema.minimum !== undefined && typeof value === 'number' && value < schema.minimum) {
      return `${path}: Value must be >= ${schema.minimum}`;
    }

    if (schema.maximum !== undefined && typeof value === 'number' && value > schema.maximum) {
      return `${path}: Value must be <= ${schema.maximum}`;
    }

    if (schema.minLength !== undefined && typeof value === 'string' && value.length < schema.minLength) {
      return `${path}: String must be at least ${schema.minLength} characters`;
    }

    if (schema.maxLength !== undefined && typeof value === 'string' && value.length > schema.maxLength) {
      return `${path}: String must be at most ${schema.maxLength} characters`;
    }

    return null;
  }
}
