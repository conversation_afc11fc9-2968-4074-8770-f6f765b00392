/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { OAuth2Client } from 'google-auth-library';
import { createServer } from 'http';
import { URL } from 'url';
import open from 'open';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
  authUrl: string;
  tokenUrl: string;
}

export interface OAuth2Tokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
  tokenType?: string;
}

export class OAuth2Manager {
  private config: OAuth2Config;

  constructor(config: OAuth2Config) {
    this.config = config;
  }

  async authenticate(): Promise<OAuth2Tokens> {
    return new Promise((resolve, reject) => {
      const server = createServer();
      const port = 8080;

      server.listen(port, () => {
        const authUrl = this.buildAuthUrl();
        console.log(`Opening browser for authentication...`);
        console.log(`If the browser doesn't open automatically, visit: ${authUrl}`);
        
        open(authUrl).catch(err => {
          console.warn('Failed to open browser automatically:', err.message);
        });
      });

      server.on('request', async (req, res) => {
        try {
          const url = new URL(req.url!, `http://localhost:${port}`);
          
          if (url.pathname === '/callback') {
            const code = url.searchParams.get('code');
            const error = url.searchParams.get('error');

            if (error) {
              res.writeHead(400, { 'Content-Type': 'text/html' });
              res.end(`<h1>Authentication Error</h1><p>${error}</p>`);
              server.close();
              reject(new Error(`OAuth2 error: ${error}`));
              return;
            }

            if (!code) {
              res.writeHead(400, { 'Content-Type': 'text/html' });
              res.end('<h1>Authentication Error</h1><p>No authorization code received</p>');
              server.close();
              reject(new Error('No authorization code received'));
              return;
            }

            try {
              const tokens = await this.exchangeCodeForTokens(code);
              
              res.writeHead(200, { 'Content-Type': 'text/html' });
              res.end(`
                <h1>Authentication Successful!</h1>
                <p>You can now close this window and return to the CLI.</p>
                <script>setTimeout(() => window.close(), 3000);</script>
              `);
              
              server.close();
              resolve(tokens);
            } catch (tokenError) {
              res.writeHead(500, { 'Content-Type': 'text/html' });
              res.end(`<h1>Token Exchange Error</h1><p>${tokenError}</p>`);
              server.close();
              reject(tokenError);
            }
          } else {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('<h1>Not Found</h1>');
          }
        } catch (requestError) {
          res.writeHead(500, { 'Content-Type': 'text/html' });
          res.end(`<h1>Server Error</h1><p>${requestError}</p>`);
          server.close();
          reject(requestError);
        }
      });

      server.on('error', (err) => {
        reject(new Error(`OAuth2 server error: ${err.message}`));
      });

      // Timeout after 5 minutes
      setTimeout(() => {
        server.close();
        reject(new Error('OAuth2 authentication timeout'));
      }, 5 * 60 * 1000);
    });
  }

  async refreshTokens(refreshToken: string): Promise<OAuth2Tokens> {
    const response = await fetch(this.config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Token refresh failed: ${errorData.error_description || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token || refreshToken,
      expiresAt: data.expires_in ? Date.now() + (data.expires_in * 1000) : undefined,
      tokenType: data.token_type,
    };
  }

  private buildAuthUrl(): string {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      response_type: 'code',
      scope: this.config.scopes.join(' '),
      access_type: 'offline',
      prompt: 'consent',
    });

    return `${this.config.authUrl}?${params.toString()}`;
  }

  private async exchangeCodeForTokens(code: string): Promise<OAuth2Tokens> {
    const response = await fetch(this.config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        redirect_uri: this.config.redirectUri,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Token exchange failed: ${errorData.error_description || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt: data.expires_in ? Date.now() + (data.expires_in * 1000) : undefined,
      tokenType: data.token_type,
    };
  }
}

// Google-specific OAuth2 configuration
export const GOOGLE_OAUTH_CONFIG: OAuth2Config = {
  clientId: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  redirectUri: 'http://localhost:8080/callback',
  scopes: ['https://www.googleapis.com/auth/generative-language'],
  authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
  tokenUrl: 'https://oauth2.googleapis.com/token',
};

export class GoogleOAuth2Manager extends OAuth2Manager {
  constructor() {
    super(GOOGLE_OAUTH_CONFIG);
  }

  async authenticateWithGoogle(): Promise<OAuth2Tokens> {
    if (!GOOGLE_OAUTH_CONFIG.clientId || !GOOGLE_OAUTH_CONFIG.clientSecret) {
      throw new Error('Google OAuth2 credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.');
    }
    
    return this.authenticate();
  }
}
