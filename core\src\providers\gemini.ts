/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { GoogleGenerativeAI, GenerativeModel, FunctionDeclaration as GeminiFunctionDeclaration } from '@google/genai';
import { BaseLLMClient, FunctionDeclaration } from './base.js';
import { 
  LLMMessage, 
  LLMResponse, 
  StreamingLLMResponse, 
  LLMClientConfig,
  LLMProvider,
  LLMModel,
  AuthMethod
} from '../types/index.js';

export const GEMINI_PROVIDER: LLMProvider = {
  name: 'gemini',
  displayName: 'Google Gemini',
  models: [
    {
      id: 'gemini-pro',
      name: 'gemini-pro',
      displayName: 'Gemini Pro',
      contextWindow: 32768,
      maxTokens: 8192,
      supportsFunctionCalling: true
    },
    {
      id: 'gemini-pro-vision',
      name: 'gemini-pro-vision',
      displayName: 'Gemini Pro Vision',
      contextWindow: 16384,
      maxTokens: 4096,
      supportsFunctionCalling: true,
      supportsVision: true
    }
  ],
  authMethods: [
    {
      type: 'api_key',
      name: 'API Key',
      description: 'Google AI Studio API key',
      required: false
    },
    {
      type: 'oauth2',
      name: 'OAuth2',
      description: 'Google OAuth2 authentication',
      required: false
    }
  ],
  supportsStreaming: true,
  supportsFunctionCalling: true
};

export class GeminiClient extends BaseLLMClient {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(config: LLMClientConfig) {
    super(config, GEMINI_PROVIDER);
    
    if (!config.apiKey) {
      throw new Error('Gemini API key is required');
    }
    
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({ 
      model: config.model,
      generationConfig: {
        temperature: config.temperature || 0.7,
        maxOutputTokens: config.maxTokens || 8192,
      }
    });
  }

  async generateResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): Promise<LLMResponse> {
    try {
      const formattedMessages = this.formatMessagesForGemini(messages);
      const tools = functions ? this.formatFunctionsForGemini(functions) : undefined;

      const chat = this.model.startChat({
        history: formattedMessages.slice(0, -1),
        tools: tools ? [{ functionDeclarations: tools }] : undefined
      });

      const lastMessage = formattedMessages[formattedMessages.length - 1];
      const result = await chat.sendMessage(lastMessage.parts);
      
      const response = await result.response;
      const text = response.text();
      
      // Handle function calls
      const functionCalls = response.functionCalls();
      const toolCalls = functionCalls?.map((call, index) => ({
        id: `call_${Date.now()}_${index}`,
        type: 'function' as const,
        function: {
          name: call.name,
          arguments: JSON.stringify(call.args)
        }
      }));

      return {
        content: text,
        toolCalls,
        usage: {
          promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
          completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: result.response.usageMetadata?.totalTokenCount || 0
        },
        finishReason: this.mapFinishReason(response.candidates?.[0]?.finishReason)
      };
    } catch (error: any) {
      throw this.handleError(error, 'generateResponse');
    }
  }

  async *generateStreamingResponse(
    messages: LLMMessage[],
    functions?: FunctionDeclaration[],
    abortSignal?: AbortSignal
  ): AsyncGenerator<StreamingLLMResponse> {
    try {
      const formattedMessages = this.formatMessagesForGemini(messages);
      const tools = functions ? this.formatFunctionsForGemini(functions) : undefined;

      const chat = this.model.startChat({
        history: formattedMessages.slice(0, -1),
        tools: tools ? [{ functionDeclarations: tools }] : undefined
      });

      const lastMessage = formattedMessages[formattedMessages.length - 1];
      const result = await chat.sendMessageStream(lastMessage.parts);

      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        const functionCalls = chunk.functionCalls();
        
        const toolCalls = functionCalls?.map((call, index) => ({
          id: `call_${Date.now()}_${index}`,
          type: 'function' as const,
          function: {
            name: call.name,
            arguments: JSON.stringify(call.args)
          }
        }));

        yield {
          content: chunkText,
          toolCalls,
          done: false
        };
      }

      const finalResponse = await result.response;
      yield {
        content: '',
        done: true,
        usage: {
          promptTokens: finalResponse.usageMetadata?.promptTokenCount || 0,
          completionTokens: finalResponse.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: finalResponse.usageMetadata?.totalTokenCount || 0
        }
      };
    } catch (error: any) {
      throw this.handleError(error, 'generateStreamingResponse');
    }
  }

  async validateCredentials(): Promise<boolean> {
    try {
      const testModel = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      const result = await testModel.generateContent('Hello');
      return !!result.response;
    } catch (error) {
      return false;
    }
  }

  private formatMessagesForGemini(messages: LLMMessage[]): any[] {
    return messages.map(msg => {
      if (msg.role === 'system') {
        // Gemini doesn't have a system role, so we'll prepend it to the first user message
        return null;
      }
      
      return {
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      };
    }).filter(Boolean);
  }

  private formatFunctionsForGemini(functions: FunctionDeclaration[]): GeminiFunctionDeclaration[] {
    return functions.map(func => ({
      name: func.name,
      description: func.description,
      parameters: func.parameters
    }));
  }

  private mapFinishReason(reason?: string): LLMResponse['finishReason'] {
    switch (reason) {
      case 'STOP':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'SAFETY':
        return 'content_filter';
      default:
        return 'stop';
    }
  }
}
